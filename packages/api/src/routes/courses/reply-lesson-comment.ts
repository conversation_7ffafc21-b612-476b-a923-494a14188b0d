import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  lessonId: z.string(),
  commentId: z.string(),
})

const bodySchema = z.object({
  content: z.string().min(1).max(1000),
})

export const replyLessonComment = new Hono()
	.use(authMiddleware)
	.post('/', validator('param', paramsSchema), validator('json', bodySchema),
		describeRoute({
			summary: "Reply to lesson comment",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const { commentId } = c.req.valid('param')
      const { content } = c.req.valid('json')

      const newReply = {
        id: Date.now().toString(),
        content,
        commentId,
        userId: 'current-user-id',
        createdAt: new Date().toISOString(),
        user: {
          id: 'current-user-id',
          name: 'Current User',
          email: '<EMAIL>',
        },
      }

      return c.json({ reply: newReply })
    } catch (error) {
      return c.json({ error: 'Failed to reply to comment' }, 500)
    }
  })

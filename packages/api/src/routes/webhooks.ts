import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { z } from "zod";
import { createUser, createUserAccount, getUserByEmail, db } from "@repo/database";
import { sendEmail } from "@repo/mail";

import { logger } from "@repo/logs";
import crypto from "crypto";

export const webhooksRouter = new Hono().post(
	"/webhooks/cakto/purchase",
	describeRoute({
		tags: ["webhooks"],
		summary: "Handle Cakto purchase webhook",
		description: `Process approved purchases from Cakto platform and grant course access.

**Expected Payload Structure:**
The webhook expects a JSON payload with the following structure:

\`\`\`json
{
  "secret": "1340098d-340d-488a-af83-f80e0eaaa773",
  "event": "purchase_approved",
  "data": {
    "id": "87956abe-940e-4e8b-8a27-82c482920f64",
    "refId": "9vbgfmg",
    "customer": {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone": "***********",
      "docNumber": "***********"
    },
    "affiliate": "<EMAIL>",
    "offer": {
      "id": "B8BcHrY",
      "name": "Special Offer",
      "price": 10
    },
    "offer_type": "main",
    "product": {
      "name": "Produto Teste",
      "id": "ff3fdf61-e88f-43b5-982a-32d50f112414",
      "short_id": "AckhQ75",
      "supportEmail": "<EMAIL>",
      "type": "unique",
      "invoiceDescription": ""
    },
    "parent_order": "95M26wi",
    "checkoutUrl": "https://pay.cakto.com.br/EXAMPLE",
    "status": "waiting_payment",
    "baseAmount": 100.0,
    "discount": 10.0,
    "amount": 90.0,
    "commissions": [
      {
        "user": "<EMAIL>",
        "totalAmount": 1.89,
        "type": "producer",
        "percentage": 80
      }
    ],
    "reason": "Motivo de recusa do cartão",
    "refund_reason": "Motivo de reembolso",
    "installments": 1,
    "paymentMethod": "credit_card",
    "paymentMethodName": "Cartão de Crédito",
    "paidAt": "2024-09-18T23:12:59.347605+00:00",
    "createdAt": "2024-09-18T23:12:59.347605+00:00",
    "card": {
      "lastDigits": "4323",
      "holderName": "Card Example",
      "brand": "visa"
    },
    "boleto": {
      "barcode": "03399853012970000154708032001011596630000000500",
      "boletoUrl": "https://urlDePagamento.example.com",
      "expirationDate": "2024-03-22"
    },
    "pix": {
      "expirationDate": "2024-03-21",
      "qrCode": "pixqrcode"
    },
    "picpay": {
      "qrCode": "picpaycode",
      "paymentURL": "https://urlDePagamento.example.com",
      "expirationDate": "2024-09-12 15:36:34-03:00"
    }
  }
}
\`\`\`

**Authentication:**
- Optional HMAC signature validation via \`X-Cakto-Signature\` header
- Secret validation via payload \`secret\` field

**Processing Logic:**
1. Validates payload structure and authentication
2. Only processes \`purchase_approved\` events (status is logged but not validated)
3. Creates user account if doesn't exist
4. Adds user to default "Cakto Members" organization
5. Maps product ID to course via \`courseProduct\` table
6. Grants course access to user
7. Sends magic link for immediate access

**Response:**
- \`200\`: Success with user and course details
- \`400\`: Invalid payload structure
- \`401\`: Invalid HMAC signature
- \`404\`: No course found for product ID
- \`500\`: Server error or missing configuration`,
		requestBody: {
			content: {
				"application/json": {
					schema: {
						type: "object",
						required: ["secret", "event", "data"],
						properties: {
							secret: {
								type: "string",
								description: "Webhook secret for validation"
							},
							event: {
								type: "string",
								enum: ["purchase_approved"],
								description: "Event type - only 'purchase_approved' is processed"
							},
							data: {
								type: "object",
								required: ["id", "refId", "customer", "product", "status", "createdAt"],
								properties: {
									id: { type: "string", description: "Purchase ID" },
									refId: { type: "string", description: "Reference ID" },
									customer: {
										type: "object",
										required: ["name", "email"],
										properties: {
											name: { type: "string" },
											email: { type: "string", format: "email" },
											phone: { type: "string" },
											docNumber: { type: "string" }
										}
									},
									product: {
										type: "object",
										required: ["name", "id"],
										properties: {
											name: { type: "string" },
											id: { type: "string", description: "Product ID used to map to course" },
											short_id: { type: "string" },
											supportEmail: { type: "string" },
											type: { type: "string" },
											invoiceDescription: { type: "string" }
										}
									},
									status: {
										type: "string",
										description: "Purchase status - must be 'approved' or 'completed' to grant access"
									},
									createdAt: { type: "string", format: "date-time" }
								}
							}
						}
					}
				}
			}
		},
		responses: {
			200: {
				description: "Webhook processed successfully",
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								success: { type: "boolean" },
								message: { type: "string" },
								data: {
									type: "object",
									properties: {
										userId: { type: "string" },
										courseId: { type: "string" },
										courseName: { type: "string" }
									}
								}
							}
						}
					}
				}
			},
			400: {
				description: "Invalid payload structure",
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								success: { type: "boolean", example: false },
								message: { type: "string", example: "Invalid payload" }
							}
						}
					}
				}
			},
			401: {
				description: "Invalid HMAC signature",
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								success: { type: "boolean", example: false },
								message: { type: "string", example: "Invalid signature" }
							}
						}
					}
				}
			},
			404: {
				description: "No course found for product ID",
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								success: { type: "boolean", example: false },
								message: { type: "string", example: "No course found for product ID: xxx" }
							}
						}
					}
				}
			},
			500: {
				description: "Server error or missing configuration",
				content: {
					"application/json": {
						schema: {
							type: "object",
							properties: {
								success: { type: "boolean", example: false },
								message: { type: "string", example: "Webhook not configured" }
							}
						}
					}
				}
			}
		}
	}),
	async (c) => {
		try {
			logger.info("Cakto webhook received");

			// Verificar se há chave secreta para autenticação HMAC
			const signature = c.req.header("X-Cakto-Signature");
			const expectedSecret = process.env.MEMBERS_WEBHOOK_SECRET;

			if (!expectedSecret) {
				logger.error("MEMBERS_WEBHOOK_SECRET not configured");
				return c.json({ success: false, message: "Webhook not configured" }, 500);
			}

			// Obter o corpo da requisição como string para validação HMAC
			const body = await c.req.text();
			const payload = JSON.parse(body);

			logger.info(`Webhook payload received - Event: ${payload.event}, Customer: ${payload.data?.customer?.email}`);

			// Validar assinatura HMAC (igual ao cakto-backend)
			if (signature) {
				const expectedSignature = crypto
					.createHmac('sha256', expectedSecret)
					.update(body)
					.digest('hex');

				if (signature !== expectedSignature) {
					logger.error("Invalid HMAC signature");
					return c.json({ success: false, message: "Invalid signature" }, 401);
				}
			}

			// Schema para validação do payload real da Cakto
			// Baseado no payload oficial: https://aluno.cakto.com.br/api/docs#tag/webhooks/post/api/webhooks/cakto/purchase
			const caktoWebhookSchema = z.object({
				secret: z.string().describe("Webhook secret for validation"),
				event: z.string().describe("Event type - only 'purchase_approved' is processed"),
				data: z.object({
					id: z.string().describe("Purchase ID"),
					refId: z.string().describe("Reference ID"),
					customer: z.object({
						name: z.string().describe("Customer full name"),
						email: z.string().email().describe("Customer email address"),
						phone: z.string().optional().describe("Customer phone number"),
						docNumber: z.string().optional().describe("Customer document number (CPF/CNPJ)"),
					}).describe("Customer information"),
					affiliate: z.string().email().optional().describe("Affiliate email address"),
					offer: z.object({
						id: z.string().describe("Offer ID"),
						name: z.string().describe("Offer name"),
						price: z.number().describe("Offer price"),
					}).optional().describe("Offer details"),
					offer_type: z.string().optional().describe("Type of offer (main, upsell, etc.)"),
					product: z.object({
						name: z.string().describe("Product name"),
						id: z.string().describe("Product ID - used to map to course"),
						short_id: z.string().optional().describe("Product short ID"),
						supportEmail: z.string().email().optional().describe("Product support email"),
						type: z.string().optional().describe("Product type"),
						invoiceDescription: z.string().optional().describe("Invoice description"),
					}).describe("Product information"),
					parent_order: z.string().optional().describe("Parent order ID"),
					checkoutUrl: z.string().url().optional().describe("Checkout URL"),
					status: z.string().describe("Purchase status - must be 'approved' or 'completed' to grant access"),
					baseAmount: z.number().optional().describe("Base amount before discount"),
					discount: z.number().optional().describe("Discount amount"),
					amount: z.number().optional().describe("Final amount after discount"),
					commissions: z.array(z.object({
						user: z.string().email().describe("Commission recipient email"),
						totalAmount: z.number().describe("Commission amount"),
						type: z.string().describe("Commission type (producer, affiliate, etc.)"),
						percentage: z.number().describe("Commission percentage"),
					})).optional().describe("Commission details"),
					reason: z.string().optional().describe("Reason for card refusal"),
					refund_reason: z.string().optional().describe("Refund reason"),
					installments: z.number().int().positive().optional().describe("Number of installments"),
					paymentMethod: z.string().optional().describe("Payment method code"),
					paymentMethodName: z.string().optional().describe("Payment method display name"),
					paidAt: z.string().datetime().optional().nullable().describe("Payment date (ISO 8601)"),
					createdAt: z.string().datetime().describe("Creation date (ISO 8601)"),
					// Payment method specific data - all optional as they depend on payment method
					card: z.object({
						lastDigits: z.string().describe("Card last 4 digits"),
						holderName: z.string().describe("Card holder name"),
						brand: z.string().describe("Card brand (visa, mastercard, etc.)"),
					}).optional().describe("Credit card details"),
					boleto: z.object({
						barcode: z.string().describe("Boleto barcode"),
						boletoUrl: z.string().url().describe("Boleto payment URL"),
						expirationDate: z.string().describe("Boleto expiration date"),
					}).optional().describe("Boleto payment details"),
					pix: z.object({
						expirationDate: z.string().describe("PIX expiration date"),
						qrCode: z.string().describe("PIX QR code"),
					}).optional().describe("PIX payment details"),
					picpay: z.object({
						qrCode: z.string().describe("PicPay QR code"),
						paymentURL: z.string().url().describe("PicPay payment URL"),
						expirationDate: z.string().describe("PicPay expiration date"),
					}).optional().describe("PicPay payment details"),
				}).describe("Purchase data"),
			});

			const validationResult = caktoWebhookSchema.safeParse(payload);
			if (!validationResult.success) {
				logger.error("Invalid webhook payload", {
					errors: validationResult.error.errors,
					payload: JSON.stringify(payload, null, 2)
				});
				return c.json({ success: false, message: "Invalid payload" }, 400);
			}

			const { data, event } = validationResult.data;
			const { customer, product, status } = data;

			logger.info(`Webhook validation successful - Product ID: ${product.id}, Product Name: ${product.name}`);

			// Processar apenas eventos de compra aprovada
			if (event !== "purchase_approved") {
				logger.info(`Webhook received but event is not purchase_approved: ${event}`);
				return c.json({
					success: true,
					message: `Webhook received but event is not purchase_approved (${event})`
				}, 200);
			}

			// Para eventos "purchase_approved", processamos independente do status
			// pois o próprio evento já indica que a compra foi aprovada
			// Apenas logamos o status para monitoramento
			logger.info(`Processing purchase_approved event with status: ${status} for customer: ${customer.email}`);

			// Verificar se o usuário já existe
			let user = await getUserByEmail(customer.email);

			// Se não existir, criar o usuário
			if (!user) {
				logger.info(`Creating new user for ${customer.email}`);

				// Criar o usuário sem senha (usará magic link)
				user = await createUser({
					email: customer.email,
					name: customer.name,
					role: "user",
					emailVerified: true,
					onboardingComplete: false,
				});

				if (!user) {
					throw new Error(`Failed to create user for ${customer.email}`);
				}

				logger.info(`User created successfully for ${customer.email}`);
			}

			// Buscar ou criar organização padrão
			let defaultOrganization = await db.organization.findFirst({
				where: { name: "Cakto Members" }
			});

			if (!defaultOrganization) {
				defaultOrganization = await db.organization.create({
					data: {
						name: "Cakto Members",
						slug: "cakto-members",
						createdAt: new Date(),
					}
				});
				logger.info(`Created default organization: ${defaultOrganization.name}`);
			}

			// Verificar se o usuário já é membro da organização
			const existingMember = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId: defaultOrganization.id,
				},
			});

			// Se não for membro, adicionar à organização
			if (!existingMember) {
				await db.member.create({
					data: {
						userId: user.id,
						organizationId: defaultOrganization.id,
						role: "member",
						createdAt: new Date(),
					},
				});
				logger.info(`User ${user.email} added to organization ${defaultOrganization.name}`);
			}

			// Buscar o curso relacionado ao produto
			logger.info(`Looking for course mapped to product ID: ${product.id}`);
			const courseProduct = await db.courseProduct.findFirst({
				where: {
					caktoProductId: product.id,
				},
				include: {
					course: true,
				},
			});

			if (!courseProduct) {
				logger.error(`No course found for product ID: ${product.id}. Make sure the product is mapped to a course in the admin panel.`);
				return c.json({
					success: false,
					message: `No course found for product ID: ${product.id}. Please contact support.`
				}, 404);
			}

			logger.info(`Course found: ${courseProduct.course.name} (ID: ${courseProduct.courseId})`);

			// Verificar se o usuário já tem acesso ao curso
			const existingAccess = await db.userCourses.findFirst({
				where: {
					userId: user.id,
					courseId: courseProduct.courseId,
				},
			});

			if (existingAccess) {
				logger.info(`User ${user.email} already has access to course ${courseProduct.course.name}`);
				return c.json({
					success: true,
					message: "User already has access to this course"
				}, 200);
			}

			// Conceder acesso ao curso
			await db.userCourses.create({
				data: {
					userId: user.id,
					courseId: courseProduct.courseId,
					finalTime: null, // Sem data de expiração
				},
			});

			logger.info(`Access granted: User ${user.email} to course ${courseProduct.course.name}`);

			// Enviar magic link para o usuário
			const auth = await import("@repo/auth").then(m => m.auth);
			const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://members.cakto.com.br";

			// Gerar magic link usando Better Auth
			try {
				await auth.api.signInMagicLink({
					body: {
						email: user.email,
						callbackURL: `${baseUrl}/app/courses/${courseProduct.courseId}`,
					},
					headers: new Headers(),
				});
				logger.info(`Magic link sent successfully to ${user.email}`);
			} catch (error) {
				logger.error(`Failed to send magic link to ${user.email}:`, error);
			}

			// Enviar email de boas-vindas com detalhes da compra
			try {
				const purchaseDate = new Date(data.createdAt).toLocaleDateString('pt-BR', {
					day: '2-digit',
					month: '2-digit',
					year: 'numeric',
					hour: '2-digit',
					minute: '2-digit'
				});

				const purchaseAmount = new Intl.NumberFormat('pt-BR', {
					style: 'currency',
					currency: 'BRL'
				}).format(data.amount || 0);

				await sendEmail({
					to: user.email,
					templateId: "welcomeCourseAccess",
					context: {
						name: user.name,
						courseName: courseProduct.course.name,
						courseId: courseProduct.courseId,
						productName: product.name,
						purchaseAmount,
						purchaseDate,
					},
					locale: "pt",
				});
				logger.info(`Welcome email sent successfully to ${user.email}`);
			} catch (error) {
				logger.error(`Failed to send welcome email to ${user.email}:`, error);
			}

			return c.json({
				success: true,
				message: "Purchase processed successfully",
				data: {
					userId: user.id,
					courseId: courseProduct.courseId,
					courseName: courseProduct.course.name,
				},
			}, 200);
		} catch (error) {
			logger.error("Error processing Cakto webhook", error);
			return c.json({
				success: false,
				message: `Error processing webhook: ${error instanceof Error ? error.message : "Unknown error"}`
			}, 500);
		}
	},
);
